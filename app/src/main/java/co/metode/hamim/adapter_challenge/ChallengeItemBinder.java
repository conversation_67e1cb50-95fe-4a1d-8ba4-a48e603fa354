package co.metode.hamim.adapter_challenge;

import android.content.Context;
import android.graphics.Bitmap;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;

import java.util.Arrays;
import java.util.List;

import co.metode.hamim.R;
import co.metode.hamim.cek_hafalan.ItemModel;

import static co.metode.hamim.adapter_challenge.ChallengeConstants.*;

/**
 * Handles binding of item data to views in the Challenge Adapter
 */
public class ChallengeItemBinder {
    
    private final Context context;
    private final AssetImageLoader assetImageLoader;
    private final HafalanManager hafalanManager;
    private final AudioDownloadManager audioDownloadManager;
    
    public ChallengeItemBinder(Context context, AssetImageLoader assetImageLoader, 
                              HafalanManager hafalanManager, AudioDownloadManager audioDownloadManager) {
        this.context = context;
        this.assetImageLoader = assetImageLoader;
        this.hafalanManager = hafalanManager;
        this.audioDownloadManager = audioDownloadManager;
    }
    
    /**
     * Bind item data to the view holder
     */
    public void bindItem(@NonNull ChallengeViewHolder holder, ItemModel item, int position, 
                        String title, String jmlAyat, int isHafal, String id_surat, 
                        String id_detail_surat, List<ItemModel> itemList) {
        
        String number = Integer.toString(position + 1);
        boolean isEven = position % 2 == 0;
        boolean shouldEnable = shouldEnablePosition(position, itemList);
        boolean isSlicingLevel = isSlicingLevel(position);
        
        if (isEven) {
            bindLeftSide(holder, item, position, title, number, isSlicingLevel, shouldEnable, 
                        isHafal, id_surat, id_detail_surat, itemList);
        } else {
            bindRightSide(holder, item, position, title, number, isSlicingLevel, shouldEnable, 
                         isHafal, id_surat, id_detail_surat, itemList);
        }
    }
    
    /**
     * Bind data for left side (even positions)
     */
    private void bindLeftSide(@NonNull ChallengeViewHolder holder, ItemModel item, int position, 
                             String title, String number, boolean isSlicingLevel, boolean shouldEnable,
                             int isHafal, String id_surat, String id_detail_surat, List<ItemModel> itemList) {
        
        holder.layout_right.setVisibility(View.GONE);
        
        setupLeftSideContent(holder, title, number, isSlicingLevel);
        setupLeftSideButton(holder, item, position, title, isSlicingLevel, shouldEnable);
        setupLeftSideHafalButton(holder, shouldEnable, id_surat, id_detail_surat, itemList);
        
        hafalanManager.cekHafal(holder, true, isHafal);
    }
    
    /**
     * Bind data for right side (odd positions)
     */
    private void bindRightSide(@NonNull ChallengeViewHolder holder, ItemModel item, int position, 
                              String title, String number, boolean isSlicingLevel, boolean shouldEnable,
                              int isHafal, String id_surat, String id_detail_surat, List<ItemModel> itemList) {
        
        holder.layout_left.setVisibility(View.GONE);
        
        setupRightSideContent(holder, title, number, isSlicingLevel);
        setupRightSideButton(holder, item, position, title, isSlicingLevel, shouldEnable);
        setupRightSideHafalButton(holder, shouldEnable, id_surat, id_detail_surat, itemList);
        
        hafalanManager.cekHafal(holder, false, isHafal);
    }
    
    /**
     * Setup content for left side
     */
    private void setupLeftSideContent(ChallengeViewHolder holder, String title, String number, boolean isSlicingLevel) {
        holder.urutanAudio.setText(TEXT_AUDIO_PREFIX + number);
        
        if (isSlicingLevel) {
            holder.ayat.setText(TEXT_SAMBUNG_PREFIX + title);
            setupButtonImage(holder.btn, assetImageLoader.getSambungAyatButtonBitmap(), 
                           assetImageLoader.isSambungAyatButtonLoaded());
        } else {
            holder.ayat.setText(title);
            setupButtonImage(holder.btn, assetImageLoader.getAyatButtonBitmap(), 
                           assetImageLoader.isAyatButtonLoaded());
        }
        
        holder.ic_tandai_hafal_right.setVisibility(View.GONE);
    }
    
    /**
     * Setup content for right side
     */
    private void setupRightSideContent(ChallengeViewHolder holder, String title, String number, boolean isSlicingLevel) {
        holder.urutanAudio2.setText(TEXT_AUDIO_PREFIX + number);
        
        if (isSlicingLevel) {
            holder.ayat2.setText(TEXT_SAMBUNG_PREFIX + title);
            setupButtonImage(holder.btn2, assetImageLoader.getSambungAyatButtonBitmap(), 
                           assetImageLoader.isSambungAyatButtonLoaded());
        } else {
            holder.ayat2.setText(title);
            setupButtonImage(holder.btn2, assetImageLoader.getAyatButtonBitmap(), 
                           assetImageLoader.isAyatButtonLoaded());
        }
        
        holder.ic_tandai_hafal_left.setVisibility(View.GONE);
    }
    
    /**
     * Setup button image (bitmap or fallback drawable)
     */
    private void setupButtonImage(android.widget.ImageView button, Bitmap bitmap, boolean isLoaded) {
        if (isLoaded && bitmap != null) {
            button.setImageBitmap(bitmap);
        } else {
            // Fallback to drawable
            button.setImageResource(R.drawable.circle_orange);
        }
    }
    
    /**
     * Setup left side button click listener
     */
    private void setupLeftSideButton(ChallengeViewHolder holder, ItemModel item, int position, 
                                   String title, boolean isSlicingLevel, boolean shouldEnable) {
        holder.btn.setEnabled(shouldEnable);
        holder.btn.setAlpha(shouldEnable ? 1.0f : 0.5f);
        
        holder.btn.setOnClickListener(v -> {
            if (shouldEnable) {
                audioDownloadManager.handleAudioPlayback(holder, item, position, isSlicingLevel, title);
            } else {
                Toast.makeText(context, MSG_SELESAIKAN_HAFALAN_SEBELUMNYA, Toast.LENGTH_SHORT).show();
            }
        });
    }
    
    /**
     * Setup right side button click listener
     */
    private void setupRightSideButton(ChallengeViewHolder holder, ItemModel item, int position, 
                                    String title, boolean isSlicingLevel, boolean shouldEnable) {
        holder.btn2.setEnabled(shouldEnable);
        holder.btn2.setAlpha(shouldEnable ? 1.0f : 0.5f);
        
        holder.btn2.setOnClickListener(v -> {
            if (shouldEnable) {
                audioDownloadManager.handleAudioPlayback(holder, item, position, isSlicingLevel, title);
            } else {
                Toast.makeText(context, MSG_SELESAIKAN_HAFALAN_SEBELUMNYA, Toast.LENGTH_SHORT).show();
            }
        });
    }
    
    /**
     * Setup left side hafal button
     */
    private void setupLeftSideHafalButton(ChallengeViewHolder holder, boolean shouldEnable, 
                                        String id_surat, String id_detail_surat, List<ItemModel> itemList) {
        if (!shouldEnable) {
            holder.tandai_hafal_left.setOnClickListener(v -> {
                Toast.makeText(context, MSG_SELESAIKAN_HAFALAN_SEBELUMNYA, Toast.LENGTH_SHORT).show();
            });
        } else {
            hafalanManager.setupTandaiHafal(holder, true, id_surat, id_detail_surat, itemList);
        }
        holder.tandai_hafal_left.setAlpha(shouldEnable ? 1.0f : 0.5f);
    }
    
    /**
     * Setup right side hafal button
     */
    private void setupRightSideHafalButton(ChallengeViewHolder holder, boolean shouldEnable, 
                                         String id_surat, String id_detail_surat, List<ItemModel> itemList) {
        if (!shouldEnable) {
            holder.tandai_hafal_right.setOnClickListener(v -> {
                Toast.makeText(context, MSG_SELESAIKAN_HAFALAN_SEBELUMNYA, Toast.LENGTH_SHORT).show();
            });
        } else {
            hafalanManager.setupTandaiHafal(holder, false, id_surat, id_detail_surat, itemList);
        }
        holder.tandai_hafal_right.setAlpha(shouldEnable ? 1.0f : 0.5f);
    }
    
    /**
     * Check if position should be enabled
     */
    private boolean shouldEnablePosition(int position, List<ItemModel> itemList) {
        for (int i = 0; i < position; i++) {
            if (itemList.get(i).getIs_hafal() != HAFAL_STATUS_COMPLETE) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Check if this is a slicing level (Sambung Ayat)
     */
    private boolean isSlicingLevel(int position) {
        return Arrays.stream(SLICING_LEVELS).anyMatch(level -> level == position);
    }
}
