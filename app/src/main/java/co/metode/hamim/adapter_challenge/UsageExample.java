package co.metode.hamim.adapter_challenge;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.List;
import co.metode.hamim.cek_hafalan.ItemModel;

/**
 * Example of how to use the refactored AdapterChallenge
 * This file shows the migration from old to new adapter
 */
public class UsageExample {
    
    private Context context;
    private RecyclerView recyclerView;
    private AdapterChallengeRefactored adapter;
    
    /**
     * Example of setting up the new adapter
     */
    public void setupAdapter(List<ItemModel> itemList, String idUser, String namaJuz) {
        // Initialize array lists
        ArrayList<String> arrayListNamaSurat = new ArrayList<>();
        ArrayList<String> arrayListUrl = new ArrayList<>();
        
        // Create the refactored adapter
        adapter = new AdapterChallengeRefactored(
            context, 
            itemList, 
            arrayListNamaSurat, 
            arrayListUrl, 
            idUser, 
            namaJuz
        );
        
        // Set up listener for hafal status changes
        adapter.setOnHafalStatusChangedListener(new HafalanManager.OnHafalStatusChangedListener() {
            @Override
            public void onHafalStatusChanged(boolean isHafal) {
                // Handle hafal status change
                if (isHafal) {
                    // User marked as hafal
                    onHafalMarked();
                } else {
                    // User unmarked hafal
                    onHafalUnmarked();
                }
            }
        });
        
        // Set adapter to RecyclerView
        recyclerView.setAdapter(adapter);
    }
    
    /**
     * Handle when user marks hafal
     */
    private void onHafalMarked() {
        // Update UI or perform actions when hafal is marked
        // For example: update progress, show congratulations, etc.
    }
    
    /**
     * Handle when user unmarks hafal
     */
    private void onHafalUnmarked() {
        // Update UI or perform actions when hafal is unmarked
        // For example: update progress, show warning, etc.
    }
    
    /**
     * Sync offline actions when network becomes available
     */
    public void syncWhenOnline() {
        if (adapter != null) {
            adapter.syncOfflineActions();
        }
    }
    
    /**
     * Clean up resources when activity/fragment is destroyed
     */
    public void cleanup() {
        if (adapter != null) {
            adapter.cleanup();
        }
    }
    
    /**
     * Migration example - how to replace old adapter with new one
     */
    public void migrationExample(List<ItemModel> itemList, String idUser, String namaJuz) {
        // OLD WAY (before refactoring):
        /*
        AdapterChallenge oldAdapter = new AdapterChallenge(
            context, 
            itemList, 
            new ArrayList<>(), 
            new ArrayList<>(), 
            idUser, 
            namaJuz
        );
        
        oldAdapter.setOnHafalStatusChangedListener(new AdapterChallenge.OnHafalStatusChangedListener() {
            @Override
            public void onHafalStatusChanged(boolean isHafal) {
                // Handle status change
            }
        });
        
        recyclerView.setAdapter(oldAdapter);
        */
        
        // NEW WAY (after refactoring):
        ArrayList<String> arrayListNamaSurat = new ArrayList<>();
        ArrayList<String> arrayListUrl = new ArrayList<>();
        
        AdapterChallengeRefactored newAdapter = new AdapterChallengeRefactored(
            context, 
            itemList, 
            arrayListNamaSurat, 
            arrayListUrl, 
            idUser, 
            namaJuz
        );
        
        newAdapter.setOnHafalStatusChangedListener(new HafalanManager.OnHafalStatusChangedListener() {
            @Override
            public void onHafalStatusChanged(boolean isHafal) {
                // Handle status change - same interface!
            }
        });
        
        recyclerView.setAdapter(newAdapter);
        
        // Don't forget to cleanup!
        // Call newAdapter.cleanup() in onDestroy()
    }
    
    /**
     * Example of activity lifecycle integration
     */
    public static class ExampleActivity {
        
        private AdapterChallengeRefactored adapter;
        
        protected void onCreate() {
            // Setup adapter
            setupAdapter();
        }
        
        protected void onResume() {
            // Sync offline actions when activity resumes
            if (adapter != null) {
                adapter.syncOfflineActions();
            }
        }
        
        protected void onDestroy() {
            // Clean up resources
            if (adapter != null) {
                adapter.cleanup();
            }
        }
        
        private void setupAdapter() {
            // Implementation here
        }
    }
    
    /**
     * Benefits of the refactored approach:
     * 
     * 1. MAINTAINABILITY:
     *    - Each class has single responsibility
     *    - Easier to find and fix bugs
     *    - Easier to add new features
     * 
     * 2. TESTABILITY:
     *    - Each component can be tested separately
     *    - Easier to mock dependencies
     *    - Better unit test coverage
     * 
     * 3. REUSABILITY:
     *    - AssetImageLoader can be used in other adapters
     *    - HafalanManager can be used for other hafalan features
     *    - AudioDownloadManager can be used for other audio features
     * 
     * 4. PERFORMANCE:
     *    - Better memory management with cleanup
     *    - Cached bitmaps for better performance
     *    - Proper resource disposal
     * 
     * 5. CODE ORGANIZATION:
     *    - Related functionality grouped together
     *    - Constants centralized
     *    - Clear separation of concerns
     */
}
