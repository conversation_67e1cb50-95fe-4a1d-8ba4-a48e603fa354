package co.metode.hamim.adapter_challenge;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import co.metode.hamim.R;
import co.metode.hamim.api.ApiClient;
import co.metode.hamim.api.ApiInterface;
import co.metode.hamim.cek_hafalan.ItemModel;
import co.metode.hamim.cek_hafalan.database.HafalanDatabase;
import co.metode.hamim.cek_hafalan.database.OfflineActionEntity;
import co.metode.hamim.complete_surat.CompleteSurat;
import co.metode.hamim.surat.detailSurat.database.SuratDetailDatabase;
import retrofit2.Call;
import retrofit2.Response;

import static co.metode.hamim.adapter_challenge.ChallengeConstants.*;

/**
 * Refactored Challenge Adapter with separated concerns
 */
public class AdapterChallengeRefactored extends RecyclerView.Adapter<ChallengeViewHolder> {
    
    private static final String TAG = LOG_TAG + "_Refactored";
    
    // Core data
    private final Context context;
    private final List<ItemModel> itemList;
    private final ArrayList<String> arrayListNamaSurat;
    private final ArrayList<String> arrayListUrl;
    private final String idUser;
    private final String namaJuz;
    
    // Databases and executors
    private final SuratDetailDatabase suratDetailDatabase;
    private final HafalanDatabase database;
    private final Executor executor;
    private final ExecutorService executorService;
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    
    // Component managers
    private final AssetImageLoader assetImageLoader;
    private final HafalanManager hafalanManager;
    private final AudioDownloadManager audioDownloadManager;
    private final ChallengeItemBinder itemBinder;
    private final FloatingMulaiHandler floatingMulaiHandler;
    
    // Listener
    private HafalanManager.OnHafalStatusChangedListener listener;
    
    public AdapterChallengeRefactored(Context context, List<ItemModel> itemList, 
                                    ArrayList<String> arrayListNamaSurat, ArrayList<String> arrayListUrl, 
                                    String idUser, String namaJuz) {
        this.context = context;
        this.itemList = itemList;
        this.arrayListNamaSurat = arrayListNamaSurat;
        this.arrayListUrl = arrayListUrl;
        this.idUser = idUser;
        this.namaJuz = namaJuz;
        
        // Initialize databases and executors
        this.suratDetailDatabase = SuratDetailDatabase.getDatabase(context);
        this.database = HafalanDatabase.getDatabase(context);
        this.executor = Executors.newSingleThreadExecutor();
        this.executorService = Executors.newSingleThreadExecutor();
        
        // Initialize component managers
        this.assetImageLoader = new AssetImageLoader(context);
        this.hafalanManager = new HafalanManager(context, database, executorService, idUser);
        this.audioDownloadManager = new AudioDownloadManager(context, suratDetailDatabase, executor, 
                                                            mainHandler, idUser, namaJuz, 
                                                            arrayListNamaSurat, arrayListUrl);
        this.itemBinder = new ChallengeItemBinder(context, assetImageLoader, hafalanManager, audioDownloadManager);
        this.floatingMulaiHandler = new FloatingMulaiHandler(context, assetImageLoader);
        
        // Initialize lists
        initializeLists();
    }
    
    /**
     * Initialize array lists with item data
     */
    private void initializeLists() {
        for (ItemModel item : itemList) {
            arrayListNamaSurat.add(item.getJumlahAyat());
            arrayListUrl.add(item.getUrl_audio());
        }
    }
    
    /**
     * Set listener for hafal status changes
     */
    public void setOnHafalStatusChangedListener(HafalanManager.OnHafalStatusChangedListener listener) {
        this.listener = listener;
        this.hafalanManager.setOnHafalStatusChangedListener(listener);
    }
    
    @NonNull
    @Override
    public ChallengeViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.daftar_surat_challenge, parent, false);
        return new ChallengeViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ChallengeViewHolder holder, int position) {
        final ItemModel item = itemList.get(position);
        String id_surat = item.getIdSurat();
        String id_detail_surat = item.getId_detail_surat();
        String title = item.getNamaSurat();
        String jumlah_ayat = item.getJumlah_ayat_potong();
        int isHafal = item.getIs_hafal();
        
        // Bind item data to views
        itemBinder.bindItem(holder, item, position, title, jumlah_ayat, isHafal, 
                           id_surat, id_detail_surat, itemList);
        
        // Handle floating "Mulai" text
        floatingMulaiHandler.handleFloatingMulaiText(holder, position, itemList);
    }
    
    @Override
    public int getItemCount() {
        return itemList.size();
    }
    
    /**
     * Sync offline actions when network becomes available
     */
    public void syncOfflineActions() {
        if (!isNetworkAvailable()) {
            return;
        }
        
        executorService.execute(() -> {
            List<OfflineActionEntity> offlineActions = database.offlineActionDao().getAllPendingActions();
            
            for (OfflineActionEntity action : offlineActions) {
                try {
                    // Call the API for each pending action
                    ApiInterface apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
                    Call<CompleteSurat> call = apiInterface.tandaiHafalRespons(
                            action.getUserId(),
                            action.getSuratId(),
                            action.getDetailSuratId());
                    
                    // Execute synchronously for offline syncing
                    Response<CompleteSurat> response = call.execute();
                    
                    if (response.isSuccessful() && response.body() != null) {
                        // Delete processed action
                        database.offlineActionDao().delete(action);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error syncing offline action", e);
                }
            }
            
            // Refresh UI on main thread after sync
            mainHandler.post(() -> {
                if (context instanceof co.metode.hamim.AudioChallenge) {
                    ((co.metode.hamim.AudioChallenge) context).refreshPage();
                }
            });
        });
    }
    
    /**
     * Check if network is available
     */
    private boolean isNetworkAvailable() {
        android.net.ConnectivityManager connectivityManager = 
            (android.net.ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            android.net.NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
            return activeNetworkInfo != null && activeNetworkInfo.isConnected();
        }
        return false;
    }
    
    /**
     * Clean up resources
     */
    public void cleanup() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
        
        if (assetImageLoader != null) {
            assetImageLoader.cleanup();
        }
    }
}
