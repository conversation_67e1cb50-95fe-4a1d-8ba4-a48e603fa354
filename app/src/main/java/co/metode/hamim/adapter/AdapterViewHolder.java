package co.metode.hamim.adapter;

import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import co.metode.hamim.R;

public class AdapterViewHolder extends RecyclerView.ViewHolder {
    public ImageView btn, btn2;
    public TextView urutanAudio, urutanAudio2, ayat, ayat2, text_hafal_left, text_hafal_right;
    public ImageView circle, ic_tandai_hafal_left, ic_tandai_hafal_right;
    public LinearLayout layout_left, layout_right, tandai_hafal_left, tandai_hafal_right;
    public String complete;

    public AdapterViewHolder(@NonNull View itemView) {
        super(itemView);
        btn = itemView.findViewById(R.id.btn_surat);
        btn2 = itemView.findViewById(R.id.btn_surat2);
        circle = itemView.findViewById(R.id.circle);
        layout_right = itemView.findViewById(R.id.layout_right);
        layout_left = itemView.findViewById(R.id.layout_left);
        urutanAudio = itemView.findViewById(R.id.urutan_audio);
        urutanAudio2 = itemView.findViewById(R.id.urutan_audio2);
        ayat = itemView.findViewById(R.id.ayat);
        ayat2 = itemView.findViewById(R.id.ayat2);
        text_hafal_left = itemView.findViewById(R.id.text_hafal_left);
        text_hafal_right = itemView.findViewById(R.id.text_hafal_right);
        ic_tandai_hafal_left = itemView.findViewById(R.id.ic_tandai_hafal_left);
        ic_tandai_hafal_right = itemView.findViewById(R.id.ic_tandai_hafal_right);
        tandai_hafal_left = itemView.findViewById(R.id.tandai_hafal_left);
        tandai_hafal_right = itemView.findViewById(R.id.tandai_hafal_right);
        complete = "0";
    }
}
