package co.metode.hamim.assets;

import android.content.Context;
import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.util.Log;

import java.io.IOException;
import java.io.InputStream;

public class AssetImageLoader {
    private static final String TAG = "AssetImageLoader";
    
    private Context context;
    private Bitmap ayatButtonBitmap;
    private Bitmap sambungAyatButtonBitmap;
    private Bitmap popUpMulaiBitmap;

    public AssetImageLoader(Context context) {
        this.context = context;
        loadAssetImages();
    }

    /**
     * Load all required asset images
     */
    private void loadAssetImages() {
        try {
            AssetManager assetManager = context.getAssets();

            // Load button-ayat.png
            InputStream ayatInputStream = assetManager.open("button-ayat.png");
            ayatButtonBitmap = BitmapFactory.decodeStream(ayatInputStream);
            ayatInputStream.close();

            // Load button-sambung-ayat.png
            InputStream sambungInputStream = assetManager.open("button-sambung-ayat.png");
            sambungAyatButtonBitmap = BitmapFactory.decodeStream(sambungInputStream);
            sambungInputStream.close();

        } catch (IOException e) {
            Log.e(TAG, "Failed to load asset images", e);
            // Fallback will be handled in getter methods
        }
    }

    /**
     * Load bitmap from asset with proper resource management
     * @param fileName Asset file name
     * @return Bitmap or null if failed
     */
    public Bitmap loadBitmapFromAsset(String fileName) {
        AssetManager assetManager = context.getAssets();
        InputStream stream = null;
        try {
            stream = assetManager.open(fileName);
            return BitmapFactory.decodeStream(stream);
        } catch (IOException e) {
            Log.e(TAG, "Error loading asset: " + fileName, e);
            return null;
        } finally {
            if (stream != null) {
                try {
                    stream.close();
                } catch (IOException e) {
                    Log.e(TAG, "Error closing stream", e);
                }
            }
        }
    }

    /**
     * Get pop-up mulai bitmap with lazy loading
     * @return Bitmap or null if failed
     */
    public Bitmap getPopUpMulaiBitmap() {
        if (popUpMulaiBitmap == null) {
            // List all assets for debugging
            try {
                String[] files = context.getAssets().list("");
                StringBuilder sb = new StringBuilder("Available assets: ");
                for (String file : files) {
                    sb.append(file).append(", ");
                }
                Log.d(TAG, sb.toString());
            } catch (IOException e) {
                Log.e(TAG, "Error listing assets", e);
            }
            
            // Load gambar dari assets
            Log.d(TAG, "Loading pop_up_mulai.png from assets...");
            popUpMulaiBitmap = loadBitmapFromAsset("pop_up_mulai.png");
            
            if (popUpMulaiBitmap != null) {
                Log.d(TAG, "Successfully loaded pop_up_mulai.png: " + 
                      popUpMulaiBitmap.getWidth() + "x" + popUpMulaiBitmap.getHeight());
            } else {
                Log.e(TAG, "Failed to load pop_up_mulai.png");
            }
        }
        return popUpMulaiBitmap;
    }

    public Bitmap getAyatButtonBitmap() {
        return ayatButtonBitmap;
    }

    public Bitmap getSambungAyatButtonBitmap() {
        return sambungAyatButtonBitmap;
    }
}
